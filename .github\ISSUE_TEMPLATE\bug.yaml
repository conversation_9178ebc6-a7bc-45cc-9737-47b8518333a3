name: 🐞 Bug Report
description: File a bug report
title: "[Bug]: "
labels: ["bug", "triage"]
assignees:
  - octocat
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
  - type: input
    id: telegram_media_downloader-version
    attributes:
      label: telegram_media_downloader version or commit
      description: What version of our software are you running?
      placeholder: ex. v2.1.5 or 550a063a7fad8723220d8681687d2363cb838d7c
    validations:
      required: true
  - type: dropdown
    id: OS
    attributes:
      label: What OS are you seeing the problem on?
      multiple: true
      options:
        - Mac
        - Windows
        - Other Linux Distro
    validations:
      required: false
  - type: input
    id: python-version
    attributes:
      label: Python Version
      description: What version of python are you running?
      placeholder: ex. 3.7.1
    validations:
      required: false
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
      value: "A bug happened!"
    validations:
      required: true
  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you expected to happen!
    validations:
      required: true
  - type: textarea
    id: current-behavior
    attributes:
      label: Current Behavior
      description: Also tell us, what is currently happening?
      placeholder: Tell us what is happening now.
    validations:
      required: true
  - type: textarea
    id: possible-solution
    attributes:
      label: Possible Solution
      description: Do you have a solution for the issue?
      placeholder: Tell us what the solution could look like.
    validations:
      required: false
  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: Tell us how to reproduce the issue?
      placeholder: Tell us how to reproduce the issue?
    validations:
      required: false
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell
