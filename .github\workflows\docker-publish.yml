name: Docker Publish

on:
  push:
    branches:
      - master
    tags:
      - 'v*'

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    outputs:
      compile_image_exists: ${{ steps.check-image.outputs.exists }}
      requirements_modified: ${{ steps.check-requirements.outputs.modified }}
      check-dockerfile: ${{ steps.check-dockerfile.outputs.dockerfile_modified }}

    steps:
    - name: Checkout
      uses: actions/checkout@v2
      with:
        fetch-depth: 2

    - name: Check if compile-image exists on Docker Hub
      id: check-image
      run: |
        EXISTS=$(curl --silent --fail --head "https://hub.docker.com/v2/repositories/${{ secrets.DOCKER_HUB_USERNAME }}/telegram_media_downloader_compile/tags/latest" > /dev/null && echo "true" || echo "false")
        echo "exists=$EXISTS" >> $GITHUB_ENV
        echo "exists=$EXISTS" >> $GITHUB_OUTPUT

    - name: Check if requirements.txt has been modified
      id: check-requirements
      run: |
        MODIFIED=$(git diff --name-only HEAD~1 HEAD | grep -w 'requirements.txt' > /dev/null && echo "true" || echo "false")
        echo "modified=$MODIFIED" >> $GITHUB_ENV
        echo "modified=$MODIFIED" >> $GITHUB_OUTPUT

    - name: Check if Dockerfile has been modified
      id: check-dockerfile
      run: |
        DOCKERFILE_MODIFIED=$(git diff --name-only HEAD~1 HEAD | grep -w 'Dockerfile' > /dev/null && echo "true" || echo "false")
        echo "dockerfile_modified=$DOCKERFILE_MODIFIED" >> $GITHUB_ENV
        echo "dockerfile_modified=$DOCKERFILE_MODIFIED" >> $GITHUB_OUTPUT

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_HUB_USERNAME }}
        password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

    - name: Print Env
      id: env_aa
      run: |
        echo ${{ steps.check-image.outputs.exists }}
        echo ${{ steps.check-requirements.outputs.modified }}
        echo ${{ steps.check-dockerfile.outputs.dockerfile_modified }}

    - name: Build and push compile-image
      if: ${{ !(steps.check-image.outputs.exists == 'true' && steps.check-requirements.outputs.modified == 'false' && steps.check-dockerfile.outputs.dockerfile_modified == 'false') }}
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        platforms: linux/amd64,linux/arm64,linux/arm/v7,linux/arm/v6,linux/386,linux/ppc64le
        target: compile-image
        tags: ${{ secrets.DOCKER_HUB_USERNAME }}/telegram_media_downloader_compile:latest

    - name: Build and push runtime-image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        platforms: linux/amd64,linux/arm64,linux/arm/v7,linux/arm/v6,linux/386,linux/ppc64le
        target: runtime-image
        tags: |
          ${{ secrets.DOCKER_HUB_USERNAME }}/telegram_media_downloader:latest
          ${{ secrets.DOCKER_HUB_USERNAME }}/telegram_media_downloader:${{ github.ref_name }}
