# Number of days of inactivity before an issue becomes stale
daysUntilStale: 90
# Number of days of inactivity before a stale issue is closed
daysUntilClose: 7
# Issues with these labels will never be considered stale
exemptLabels:
  - enhancement
  - feature_request
  - help wanted
  - good first issue
  - dependencies
  - bug
# Label to use when marking as stale
staleLabel: stale
# Comment to post when marking an issue as stale. Set to `false` to disable
markComment: >
  This issue has been automatically marked as stale because it has not had
  recent activity in the past 45 days. It will be closed if no further activity
  occurs in the next 7 days. Thank you for your contributions.

# Limit to only `issues`
only: issues
