name: Unittest

on:
  push:
    branches: [ master ]
    paths-ignore:
      - 'README.md'
  pull_request:
    branches: [ master ]
    paths-ignore:
      - 'README.md'

jobs:
  build:

    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12' ]
    name: Test - Python ${{ matrix.python-version }} on ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Get setuptools Unix
      if: ${{ matrix.os != 'windows-latest' }}
      run: python -m pip install --upgrade pip setuptools codecov
    - name: Get setuptools Windows
      if: ${{ matrix.os == 'windows-latest' }}
      run: python -m pip install --upgrade pip setuptools codecov
    - name: Install dependencies
      run: make dev_install
    - name: Test with pytest
      run: |
        make -e test
        codecov
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
