<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>login</title>
  <link href="{{ url_for('static', filename='login/css/index.css') }}" rel="stylesheet">
</head>

<body>
  <div class="main">
    <div class="content">
      <div class="logo">
        <img src="{{ url_for('static', filename='login/images/login.svg') }}" alt="">
        <h1 class="title">Telegram Media Downloader</h1>
        <div class="tips">enter your password.</div>
      </div>
      <div class="password-input">
        <input type="password" class="input-p" placeholder="please enter your password" id="password">
        <div class="tip">Your password</div>
      </div>
      <button class="confirm-btn" onclick="login()">confirm</button>
    </div>
  </div>

  <script src="../static/layui/layui.js"></script>
  <script src="../static/aes/crypto-js-master/crypto-js.js"></script>
  <script src="../static/request/index.js"></script>
  <script>
    const $ = layui.$
    const layer = layui.layer

    //加密方法
    const encrypt = (word) => {
      const key = CryptoJS.enc.Utf8.parse("1234123412ABCDEF");
      const iv = CryptoJS.enc.Utf8.parse('ABCDEF1234123412');
      let srcs = CryptoJS.enc.Utf8.parse(word);
      let encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
      let hexStr = encrypted.ciphertext.toString();
      let base64Str = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Hex.parse(hexStr));
      return base64Str;
    }

    const login = () => {
      const pValue = $('#password').val()

      if (!pValue) {
        layer.msg('please enter your password!')
        return
      } else {
        const encryptData = encrypt(pValue)
        request('login', 'post', { password: encryptData }).then((res) => {
          const { code } = res
          if (code === '1') {
            const { host } = window.location;
            window.location.href = 'http://' + host
          } else {
            layer.msg('password error!')
          }
          $('#password').val('')
        })
      }
    }
  </script>
</body>

</html>
